<template>
  <CPanel class="monitoring-alarm">
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>监测告警</span>
        <span class="header-subtitle">MONITORING AND ALARMING</span>
      </div>
    </template>
    <template #content>
      <div class="wh-full relative">
        <el-date-picker
          v-model="filterTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束"
          size="small"
          @change="handleDateChange"
        />
        <CEcharts ref="chartRef" :option="option" class="monitoring-chart" />
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import dayjs from 'dayjs'

const option = ref({})
const chartRef = ref()

// 属性 - 筛选时间
const filterTime = ref([
  dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
])

// 模拟数据生成函数
const generateMockData = (startDate, endDate) => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const days = end.diff(start, 'day') + 1

  const data = []
  const labels = []

  for (let i = 0; i < days; i++) {
    const currentDate = start.add(i, 'day')
    labels.push(currentDate.format('YYYY-MM-DD'))
    // 生成随机告警数据 (0-15次)
    data.push(Math.floor(Math.random() * 16))
  }

  return { data, labels }
}

// 数据 - 图表数据源
const chartData = ref([])

// 方法 - 处理时间变化
const handleDateChange = () => {
  if (filterTime.value && filterTime.value.length === 2) {
    const mockData = generateMockData(filterTime.value[0], filterTime.value[1])
    chartData.value = mockData.data
    updateChart(mockData.data, mockData.labels)
  }
}

// 更新图表
const updateChart = (data, labels) => {
  option.value = createMonitoringChart(data, labels)
}

const createMonitoringChart = (data, labels) => {
  return {
    color: ['#00ffaf', '#306fff'],
    legend: {
      show: false
    },
    grid: {
      top: 40,
      bottom: 30
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        fontSize: 10,
        color: '#fff',
        formatter: function(value) {
          return value.substring(5) // 只显示月-日
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '次',
      nameTextStyle: {
        color: '#fff',
        padding: [0, 20, 0, 0]
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '监测警告',
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#00ffaf'
        },
        itemStyle: {
          color: '#00ffaf'
        }
      }
    ]
  }
}

onMounted(() => {
  option.value = createMonitoringChart()
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #C5D6E6;
    margin-left: 8px;
  }
}

.content-container {
  padding: 16px 0;
}

.date-selector {
  display: flex;
  align-items: center;
  width: 11rem;
  gap: 8px;
  padding: 3px 7px;
  border: 1px solid #fff;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;

  .date-icon {
    font-size: 14px;
  }

  .date-text {
    color: #fff;
  }
}

.monitoring-chart {
  height: 200px;
  width: 100%;
}
.time-selector {
  display: flex;
  justify-content: flex-end;
}
</style>
