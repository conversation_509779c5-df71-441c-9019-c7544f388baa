<!-- 视频监控 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>视频监控</span>
        <span class="header-subtitle">VIDEO SURVEILLANCE</span>
      </div>
    </template>
    <template #content>
      <div class="video-surveillance">
        <!-- 左侧视频区域 -->
        <div class="video-container">
          <video ref="videoRef" :src="videoSrc" autoplay loop muted class="main-video"></video>
          <div class="video-info">
            <span class="location-name"></span>
            <span class="time">{{ currentTime }}</span>
          </div>
        </div>

        <!-- 右侧监控点选择区域 -->
        <div class="monitor-points">
          <div v-for="(point, index) in monitorPoints" :key="index" class="monitor-point"
            :class="{ active: selectedPoint === index }" @click="selectPoint(index)">
            <img :src="point.image" :alt="point.name" class="point-image" />
            <div class="point-name">{{ point.name }}</div>
            <!-- 选中状态的对勾 -->
            <div v-if="selectedPoint === index" class="check-mark">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="10" fill="#00D4FF" />
                <path d="M6 10L8.5 12.5L14 7" stroke="white" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'

// 视频源
const videoSrc = new URL('@/assets/images/demo.mp4', import.meta.url).href

// 当前选中的监控点
const selectedPoint = ref(0)

// 当前时间
const currentTime = ref('')

// 监控点数据
const monitorPoints = ref([
  {
    name: '电影院旁桥',
    image: new URL('@/assets/images/bg_panel2_poster.jpg', import.meta.url).href
  },
  {
    name: '华悦山湖锦悦',
    image: new URL('@/assets/images/bg_panel2_poster.jpg', import.meta.url).href
  }
])

// 选择监控点
const selectPoint = (index) => {
  selectedPoint.value = index
}

// 更新时间
const updateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.video-surveillance {
  display: flex;
  height: 100%;
  gap: 15px;
}

.video-container {
  flex: 1;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
}

.main-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.video-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;

  .location-name {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .time {
    color: #C5D6E6;
    font-size: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
}

.monitor-points {
  width: 140px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.monitor-point {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    border-color: rgba(0, 212, 255, 0.5);
    transform: scale(1.02);
  }

  &.active {
    border-color: #00D4FF;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
  }
}

.point-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.point-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #fff;
  font-size: 12px;
  padding: 15px 8px 8px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.check-mark {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;

  svg {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
}
</style>
