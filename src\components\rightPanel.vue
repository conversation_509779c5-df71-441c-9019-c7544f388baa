<!-- 右侧数据面板 -->
<template>
  <div class="right-panel">
   <!-- 监测类型 -->
    <right1 />
   <!-- 桥隧健康度 -->
    <right2 />
    <!-- 桥隧病害处理 -->
    <right3 />
  </div>
</template>

<script setup>
import right1 from './rightPanel/right1.vue'
import right2 from './rightPanel/right2.vue'
import right3 from './rightPanel/right3.vue'
</script>

<style lang="scss" scoped>
.right-panel {
  position: absolute;
  width: 500px;
  height: 100%;
  right: 0;
  top: 0;
  display: grid;
  gap: 24px;
  padding: 111px 0 24px 0;
  box-sizing: border-box;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(3, 1fr);
}
</style>
